{"version": 3, "file": "node-extractor.js", "sourceRoot": "", "sources": ["../../../../src/tools/figma/lib/node-extractor.ts"], "names": [], "mappings": "AAAA;;GAEG;AAYH;;GAEG;AACH,SAAS,cAAc,CAAC,IAAoB,EAAE,SAAsC,EAAE,MAAuB;IAC3G,IAAI,MAAM,EAAE,CAAC;QACX,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IACjC,CAAC;IACD,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,cAAc,CAAC,KAAK,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;AAC1E,CAAC;AAED;;GAEG;AACH,SAAS,uBAAuB,CAC9B,IAAoB,EACpB,SAAsC,EACtC,UAAsB,EACtB,iBAAqC;IAErC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QACvD,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,CAAC,CAAA;IACvC,CAAC;IACD,sBAAsB;IACtB,IAAI,YAAY,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,CAAC;QACnC,mBAAmB,CAAC,IAAI,EAAE,UAAU,EAAE,iBAAiB,CAAC,CAAC;IAC3D,CAAC;IACD,uBAAuB;SAClB,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,SAAS,EAAE,CAAC;QACtG,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACtC,IAAI,MAAM,IAAI,6BAA6B,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE,CAAC;YAChE,mBAAmB,CAAC,MAAM,EAAE,UAAU,EAAE,iBAAiB,CAAC,CAAC;QAC7D,CAAC;aAAM,CAAC;YACN,mBAAmB,CAAC,IAAI,EAAE,UAAU,EAAE,iBAAiB,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAED,UAAU;IACV,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,uBAAuB,CAAC,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,iBAAiB,CAAC,CAAC,CAAC;AAC5G,CAAC;AAED;;GAEG;AACH,SAAS,mBAAmB,CAAC,cAA8B,EAAE,UAAsB,EAAE,iBAAqC;IACxH,MAAM,gBAAgB,GAAG,sBAAsB,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;IAC5E,IAAI,gBAAgB,EAAE,CAAC;QACrB,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAC3C,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,wBAAwB,CAAC,MAAwB;IAC/D,MAAM,iBAAiB,GAAuB,EAAE,CAAC;IACjD,MAAM,SAAS,GAAG,IAAI,GAAG,EAA0B,CAAC;IAEpD,aAAa;IACb,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;IAE9D,cAAc;IACd,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,uBAAuB,CAAC,IAAI,EAAE,SAAS,EAAE,MAAM,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAC,CAAC;IAE7G,cAAc;IACd,MAAM,WAAW,GAAG,IAAI,GAAG,EAA4B,CAAC;IACxD,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IAEtE,OAAO,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;AAC1C,CAAC;AAED;;GAEG;AACH,SAAS,YAAY,CAAC,IAAoB,EAAE,UAAsB;IAChE,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;QACf,MAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,SAAS,UAAU,CAAC,IAAoB;IACtC,OAAO,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,CAAC;AAC/E,CAAC;AAED;;;GAGG;AACH,SAAS,6BAA6B,CAAC,MAAsB,EAAE,UAAsB;IACnF,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACrD,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,EAAE,UAAU,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;AAC9F,CAAC;AAED;;GAEG;AACH,SAAS,sBAAsB,CAAC,IAAoB,EAAE,UAAsB;IAC1E,MAAM,QAAQ,GAAqB;QACjC,MAAM,EAAE,IAAI,CAAC,EAAE;QACf,QAAQ,EAAE,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC;QACrC,QAAQ,EAAE,IAAI,CAAC,IAAI;QACnB,QAAQ,EAAE,IAAI,CAAC,IAAI;KACpB,CAAC;IAEF,kBAAkB;IAClB,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;QACf,MAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACzB,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpF,IAAI,SAAS,EAAE,CAAC;gBACd,QAAQ,CAAC,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;gBACvC,QAAQ,CAAC,QAAQ,GAAG,eAAe,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;gBAC9D,OAAO,QAAQ,CAAC;YAClB,CAAC;QACH,CAAC;IACH,CAAC;IAED,iBAAiB;IACjB,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;QACxD,QAAQ,CAAC,QAAQ,GAAG,eAAe,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC9D,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,aAAa;IACb,QAAQ,CAAC,QAAQ,GAAG,eAAe,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC9D,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAS,gBAAgB,CAAC,IAAY;IACpC,OAAO,IAAI;SACR,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC,SAAS;SAC3C,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,YAAY;SACjC,WAAW,EAAE;SACb,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO;AAC9B,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CAAC,QAAgB,EAAE,SAAiB;IAC1D,MAAM,YAAY,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,SAAS,EAAE,CAAC,CAAC;IACtE,OAAO,YAAY,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,IAAI,SAAS,EAAE,CAAC;AAC9D,CAAC"}